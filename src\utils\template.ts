export function renderHTMLTemplate(title: string, content: string, includeFramerMotion: boolean = false): string {
  const framerMotionScript = includeFramerMotion ? `
    <script type="module">
      import { motion, AnimatePresence } from 'https://esm.sh/framer-motion@11.0.24';
      window.motion = motion;
      window.AnimatePresence = AnimatePresence;
    </script>
  ` : '';

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <script src="https://cdn.tailwindcss.com"></script>
      <link rel="stylesheet" href="/styles.css">
      ${framerMotionScript}
    </head>
    <body>
      ${content}
    </body>
    </html>
  `;
}
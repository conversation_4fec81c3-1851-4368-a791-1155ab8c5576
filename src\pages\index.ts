import { renderHTMLTemplate } from '../utils/template';

export function getIndexPage(): string {
  const content = `
    <div id="top-buttons" class="absolute top-4 right-4 flex space-x-3">
      <button id="login-btn" class="bg-black text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors">
        登录
      </button>
      <button id="register-btn" class="bg-white text-black px-6 py-2 rounded-full text-sm font-medium border border-gray-300 hover:bg-gray-50 transition-colors">
        免费注册
      </button>
    </div>
    <div class="min-h-screen flex items-center justify-center">
      <h1 id="main-title" class="text-2xl font-semibold text-gray-800">Hello World</h1>
    </div>
    
    <script type="module">
      import { motion } from 'https://esm.sh/framer-motion@11.0.24';

      // Animate top buttons
      const topButtons = document.getElementById('top-buttons');
      motion(topButtons, {
        initial: { opacity: 0, x: 20 },
        animate: { opacity: 1, x: 0 },
        transition: { duration: 0.5 }
      });

      // Animate main title
      const mainTitle = document.getElementById('main-title');
      motion(mainTitle, {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.6 }
      });

      // Add button interactions
      const loginBtn = document.getElementById('login-btn');
      const registerBtn = document.getElementById('register-btn');
      
      loginBtn.addEventListener('click', () => {
        window.location.href = '/login#login';
      });
      
      registerBtn.addEventListener('click', () => {
        window.location.href = '/login#register';
      });

      // Add hover effects
      [loginBtn, registerBtn].forEach(btn => {
        btn.addEventListener('mouseenter', () => {
          motion(btn, {
            scale: 1.05,
            transition: { duration: 0.2 }
          });
        });
        
        btn.addEventListener('mouseleave', () => {
          motion(btn, {
            scale: 1,
            transition: { duration: 0.2 }
          });
        });
      });
    </script>
  `;
  
  return renderHTMLTemplate('Index', content, true);
}
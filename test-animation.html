<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="text-center">
        <h1 id="title" class="text-4xl font-bold text-gray-800 mb-8">Animation Test</h1>
        <div id="box" class="w-32 h-32 bg-blue-500 mx-auto mb-8 rounded-lg"></div>
        <button id="test-btn" class="bg-green-500 text-white px-6 py-3 rounded-lg font-medium">
            Test Animation
        </button>
        <div id="status" class="mt-4 text-gray-600"></div>
    </div>

    <script type="module">
        import { animate } from 'https://esm.sh/framer-motion@12.23.11';
        
        document.addEventListener('DOMContentLoaded', () => {
            const status = document.getElementById('status');
            
            try {
                // Test initial animations
                const title = document.getElementById('title');
                const box = document.getElementById('box');
                const btn = document.getElementById('test-btn');
                
                if (title) {
                    animate(title, 
                        { opacity: [0, 1], y: [-20, 0] },
                        { duration: 0.6 }
                    );
                }
                
                if (box) {
                    animate(box,
                        { opacity: [0, 1], scale: [0.8, 1] },
                        { duration: 0.8, delay: 0.2 }
                    );
                }
                
                if (btn) {
                    animate(btn,
                        { opacity: [0, 1], y: [20, 0] },
                        { duration: 0.6, delay: 0.4 }
                    );
                    
                    // Add click animation
                    btn.addEventListener('click', () => {
                        animate(box,
                            { rotate: [0, 360], scale: [1, 1.2, 1] },
                            { duration: 1 }
                        );
                    });
                    
                    // Add hover effects
                    btn.addEventListener('mouseenter', () => {
                        animate(btn, { scale: 1.05 }, { duration: 0.2 });
                    });
                    
                    btn.addEventListener('mouseleave', () => {
                        animate(btn, { scale: 1 }, { duration: 0.2 });
                    });
                }
                
                status.textContent = '✅ Framer Motion 12.x loaded successfully!';
                status.className = 'mt-4 text-green-600';
                
            } catch (error) {
                console.error('Animation error:', error);
                status.textContent = '❌ Animation failed: ' + error.message;
                status.className = 'mt-4 text-red-600';
            }
        });
    </script>
</body>
</html>

import React from 'react';
import { motion } from 'framer-motion';

export const IndexPage: React.FC = () => {
  const handleLoginClick = () => {
    window.location.href = '/login#login';
  };

  const handleRegisterClick = () => {
    window.location.href = '/login#register';
  };

  return (
    <>
      <motion.div 
        className="absolute top-4 right-4 flex space-x-3"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.button 
          onClick={handleLoginClick}
          className="bg-black text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          登录
        </motion.button>
        <motion.button 
          onClick={handleRegisterClick}
          className="bg-white text-black px-6 py-2 rounded-full text-sm font-medium border border-gray-300 hover:bg-gray-50 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          免费注册
        </motion.button>
      </motion.div>
      <div className="min-h-screen flex items-center justify-center">
        <motion.h1 
          className="text-2xl font-semibold text-gray-800"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Hello World
        </motion.h1>
      </div>
    </>
  );
};
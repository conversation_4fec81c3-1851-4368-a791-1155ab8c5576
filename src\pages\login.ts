import { renderHTMLTemplate } from '../utils/template';

export function getLoginPage(): string {
  const content = `
    <style>
      .form-transition {
        will-change: opacity, transform;
        transition: opacity 0.3s ease-out, transform 0.3s ease-out;
      }

      /* 创建一个内部容器来处理动画，保持原有的form-container样式不变 */
      .forms-wrapper {
        position: relative;
        min-height: 400px; /* 确保有足够高度容纳所有表单 */
      }

      /* 表单绝对定位，但不影响外层容器 */
      .forms-wrapper > div {
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
      }
    </style>

    <div class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
      <div id="form-container" class="form-container">
        <div class="forms-wrapper">
        
        <!-- 初始登录页面 -->
        <div id="loginInitial" class="text-center form-transition" style="opacity: 0; pointer-events: none;">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">欢迎回来</h2>
          <form class="space-y-6">
            <div class="input-container">
              <input 
                id="loginEmail"
                type="email" 
                placeholder="电子邮件地址" 
                class="w-full px-4 py-4 rounded-full text-center text-gray-600 focus:outline-none transition-all duration-200"
                required
              >
              <label class="input-label">电子邮件地址</label>
            </div>
            <button type="button" id="loginEmailBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              继续
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            还没有帐户？ 
            <button id="switchToRegisterBtn" class="text-blue-600 hover:underline">请注册</button>
          </p>
        </div>

        <!-- 登录密码输入页面 -->
        <div id="loginPassword" class="text-center form-transition" style="opacity: 0; pointer-events: none;">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">输入密码</h2>
          <form class="space-y-6">
            <div class="relative">
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>电子邮件地址</span>
              </div>
              <div class="flex items-center border border-gray-300 rounded-full px-4 py-3 bg-gray-50">
                <span id="displayLoginEmail" class="flex-1 text-gray-700"></span>
                <button type="button" id="editLoginEmailBtn" class="text-blue-600 hover:underline text-sm font-medium">编辑</button>
              </div>
            </div>
            <div>
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>密码</span>
              </div>
              <div class="relative input-container">
                <input 
                  id="loginPasswordInput"
                  type="password" 
                  class="w-full px-4 py-4 rounded-full text-gray-600 focus:outline-none transition-all duration-200"
                  required
                >
                <label class="input-label">密码</label>
              </div>
            </div>
            <div class="text-left">
              <button type="button" id="forgotPasswordBtn" class="text-blue-600 hover:underline text-sm">忘记了密码？</button>
            </div>
            <button type="button" id="loginSubmitBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              继续
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            还没有帐户？ 
            <button id="switchToRegisterBtn2" class="text-blue-600 hover:underline">请注册</button>
          </p>
        </div>

        <!-- 初始注册页面 -->
        <div id="registerInitial" class="text-center form-transition" style="opacity: 0; pointer-events: none;">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">创建帐户</h2>
          <form class="space-y-6">
            <div class="input-container">
              <input 
                id="registerEmail"
                type="email" 
                placeholder="电子邮件地址" 
                class="w-full px-4 py-4 rounded-full text-center text-gray-600 focus:outline-none transition-all duration-200"
                required
              >
              <label class="input-label">电子邮件地址</label>
            </div>
            <button type="button" id="registerEmailBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              继续
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            已有帐户？ 
            <button id="switchToLoginBtn" class="text-blue-600 hover:underline">请登录</button>
          </p>
        </div>

        <!-- 注册密码设置页面 -->
        <div id="registerPassword" class="text-center form-transition" style="opacity: 0; pointer-events: none;">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">创建帐户</h2>
          <form class="space-y-6">
            <div class="relative">
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>电子邮件地址</span>
              </div>
              <div class="flex items-center border border-gray-300 rounded-full px-4 py-3 bg-gray-50">
                <span id="displayRegisterEmail" class="flex-1 text-gray-700"></span>
                <button type="button" id="editRegisterEmailBtn" class="text-blue-600 hover:underline text-sm font-medium">编辑</button>
              </div>
            </div>
            <div>
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>密码</span>
              </div>
              <div class="relative input-container">
                <input 
                  id="registerPasswordInput"
                  type="password" 
                  class="w-full px-4 py-4 rounded-full text-gray-600 focus:outline-none transition-all duration-200"
                  required
                >
                <label class="input-label">密码</label>
              </div>
            </div>
            <button type="button" id="registerSubmitBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              继续
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            已有帐户？ 
            <button id="switchToLoginBtn2" class="text-blue-600 hover:underline">请登录</button>
          </p>
        </div>
        </div>
      </div>
    </div>

    <script>
      console.log('Login page script loading...');
      
      // 全局变量
      let animate = null;
      
      // 改进的动画函数
      function hideAllForms() {
        const forms = ['loginInitial', 'loginPassword', 'registerInitial', 'registerPassword'];
        forms.forEach(id => {
          const element = document.getElementById(id);
          if (element) {
            element.style.opacity = '0';
            element.style.pointerEvents = 'none';
          }
        });
      }

      function fadeOut(element) {
        return new Promise((resolve) => {
          if (!element || !animate) {
            resolve();
            return;
          }

          animate(element,
            {
              opacity: [parseFloat(element.style.opacity) || 1, 0],
              y: [0, -10]
            },
            {
              duration: 0.25,
              ease: "easeInOut"
            }
          ).then(() => {
            element.style.pointerEvents = 'none';
            resolve();
          });
        });
      }

      function fadeIn(element) {
        return new Promise((resolve) => {
          if (!element || !animate) {
            resolve();
            return;
          }

          element.style.pointerEvents = 'auto';
          animate(element,
            {
              opacity: [0, 1],
              y: [15, 0]
            },
            {
              duration: 0.4,
              ease: "easeOut"
            }
          ).then(() => {
            resolve();
          });
        });
      }

      async function switchForm(fromFormId, toFormId) {
        const fromForm = fromFormId ? document.getElementById(fromFormId) : null;
        const toForm = document.getElementById(toFormId);

        if (!toForm) return;

        // 如果有当前显示的表单，先淡出
        if (fromForm && fromForm.style.opacity !== '0') {
          await fadeOut(fromForm);
        }

        // 隐藏所有其他表单
        hideAllForms();

        // 短暂延时后淡入新表单
        setTimeout(async () => {
          await fadeIn(toForm);
        }, 50);
      }
      
      function switchToRegister() {
        console.log('Switching to register');
        const currentForm = getCurrentVisibleForm();
        switchForm(currentForm, 'registerInitial');
        window.location.hash = '#register';
      }

      function switchToLogin() {
        console.log('Switching to login');
        const currentForm = getCurrentVisibleForm();
        switchForm(currentForm, 'loginInitial');
        window.location.hash = '#login';
      }

      function getCurrentVisibleForm() {
        const forms = ['loginInitial', 'loginPassword', 'registerInitial', 'registerPassword'];
        for (const formId of forms) {
          const element = document.getElementById(formId);
          if (element && element.style.opacity !== '0') {
            return formId;
          }
        }
        return null;
      }
      
      function handleLoginEmail() {
        console.log('Handling login email');
        const email = document.getElementById('loginEmail').value;
        if (!email) {
          alert('请输入邮箱地址');
          return;
        }
        document.getElementById('displayLoginEmail').textContent = email;
        const currentForm = getCurrentVisibleForm();
        switchForm(currentForm, 'loginPassword');
      }

      function handleRegisterEmail() {
        console.log('Handling register email');
        const email = document.getElementById('registerEmail').value;
        if (!email) {
          alert('请输入邮箱地址');
          return;
        }
        document.getElementById('displayRegisterEmail').textContent = email;
        const currentForm = getCurrentVisibleForm();
        switchForm(currentForm, 'registerPassword');
      }

      function editLoginEmail() {
        const currentForm = getCurrentVisibleForm();
        switchForm(currentForm, 'loginInitial');
      }

      function editRegisterEmail() {
        const currentForm = getCurrentVisibleForm();
        switchForm(currentForm, 'registerInitial');
      }
      
      function handleLoginSubmit() {
        alert('登录功能正在开发中...');
      }
      
      function handleRegisterSubmit() {
        alert('注册功能正在开发中...');
      }
      
      function forgotPassword() {
        alert('重置密码功能正在开发中...');
      }
      
      // DOM加载完成后初始化
      document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, binding events...');
        
        // 绑定所有按钮事件
        const loginEmailBtn = document.getElementById('loginEmailBtn');
        if (loginEmailBtn) {
          loginEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Login email button clicked');
            handleLoginEmail();
          });
        }
        
        const registerEmailBtn = document.getElementById('registerEmailBtn');
        if (registerEmailBtn) {
          registerEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Register email button clicked');
            handleRegisterEmail();
          });
        }
        
        const switchToRegisterBtn = document.getElementById('switchToRegisterBtn');
        if (switchToRegisterBtn) {
          switchToRegisterBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Switch to register button clicked');
            switchToRegister();
          });
        }
        
        const switchToRegisterBtn2 = document.getElementById('switchToRegisterBtn2');
        if (switchToRegisterBtn2) {
          switchToRegisterBtn2.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Switch to register button 2 clicked');
            switchToRegister();
          });
        }
        
        const switchToLoginBtn = document.getElementById('switchToLoginBtn');
        if (switchToLoginBtn) {
          switchToLoginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Switch to login button clicked');
            switchToLogin();
          });
        }
        
        const switchToLoginBtn2 = document.getElementById('switchToLoginBtn2');
        if (switchToLoginBtn2) {
          switchToLoginBtn2.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Switch to login button 2 clicked');
            switchToLogin();
          });
        }
        
        const editLoginEmailBtn = document.getElementById('editLoginEmailBtn');
        if (editLoginEmailBtn) {
          editLoginEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            editLoginEmail();
          });
        }
        
        const editRegisterEmailBtn = document.getElementById('editRegisterEmailBtn');
        if (editRegisterEmailBtn) {
          editRegisterEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            editRegisterEmail();
          });
        }
        
        const loginSubmitBtn = document.getElementById('loginSubmitBtn');
        if (loginSubmitBtn) {
          loginSubmitBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleLoginSubmit();
          });
        }
        
        const registerSubmitBtn = document.getElementById('registerSubmitBtn');
        if (registerSubmitBtn) {
          registerSubmitBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleRegisterSubmit();
          });
        }
        
        const forgotPasswordBtn = document.getElementById('forgotPasswordBtn');
        if (forgotPasswordBtn) {
          forgotPasswordBtn.addEventListener('click', function(e) {
            e.preventDefault();
            forgotPassword();
          });
        }
        
        // 初始化显示
        hideAllForms();

        // 根据URL hash决定显示哪个表单
        const hash = window.location.hash;
        if (hash === '#register') {
          setTimeout(() => fadeIn(document.getElementById('registerInitial')), 100);
        } else if (hash === '#login' || hash === '') {
          setTimeout(() => fadeIn(document.getElementById('loginInitial')), 100);
        } else {
          // 默认显示登录页面
          setTimeout(() => fadeIn(document.getElementById('loginInitial')), 100);
        }

        // 监听hash变化
        window.addEventListener('hashchange', function() {
          const currentForm = getCurrentVisibleForm();
          const newHash = window.location.hash;

          if (newHash === '#register') {
            switchForm(currentForm, 'registerInitial');
          } else {
            switchForm(currentForm, 'loginInitial');
          }
        });

        console.log('All events bound successfully');
      });
    </script>
    
    <script type="module">
      import { animate as framerAnimate } from 'https://esm.sh/framer-motion@12.23.11';

      // 设置全局animate变量
      animate = framerAnimate;

      document.addEventListener('DOMContentLoaded', function() {
        console.log('Framer Motion loaded');

        try {
          // 初始化动画
          const formContainer = document.getElementById('form-container');
          if (formContainer) {
            framerAnimate(formContainer,
              { opacity: [0, 1], scale: [0.95, 1] },
              { duration: 0.5 }
            );
          }
        } catch (error) {
          console.error('Animation initialization failed:', error);
        }
      });
    </script>
  `;
  
  return renderHTMLTemplate('登录注册', content, true);
}
import { renderHTMLTemplate } from '../utils/template';

export function getHomePage(): string {
  const content = `
    <div class="min-h-screen flex items-center justify-center">
      <h1 id="greeting" class="text-4xl font-bold text-gray-800">你好</h1>
    </div>
    <script type="module">
      import { motion } from 'https://esm.sh/framer-motion@11.0.24';

      const greeting = document.getElementById('greeting');
      const motionH1 = motion(greeting, {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.6 }
      });
    </script>
  `;
  
  return renderHTMLTemplate('Home', content, true);
}
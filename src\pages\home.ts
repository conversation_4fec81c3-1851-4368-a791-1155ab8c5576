import { renderHTMLTemplate } from '../utils/template';

export function getHomePage(): string {
  const content = `
    <div class="min-h-screen flex items-center justify-center">
      <h1 id="greeting" class="text-4xl font-bold text-gray-800">你好</h1>
    </div>
    <script type="module">
      import { animate } from 'https://esm.sh/framer-motion@12.23.11';

      document.addEventListener('DOMContentLoaded', () => {
        try {
          const greeting = document.getElementById('greeting');
          if (greeting) {
            animate(greeting,
              { opacity: [0, 1], y: [20, 0] },
              { duration: 0.6 }
            );
          }
        } catch (error) {
          console.error('Animation initialization failed:', error);
        }
      });
    </script>
  `;
  
  return renderHTMLTemplate('Home', content, true);
}
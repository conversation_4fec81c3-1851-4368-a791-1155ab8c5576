<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>尺寸对比测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">登录页面尺寸对比测试</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 参考尺寸（基于原始CSS） -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-semibold mb-4 text-center">参考尺寸（原始设计）</h2>
                <div class="bg-gray-50 flex items-center justify-center p-4 rounded-lg">
                    <div class="form-container-reference">
                        <div class="text-center">
                            <h3 class="text-2xl font-semibold text-gray-800 mb-10">欢迎回来</h3>
                            <form class="space-y-6">
                                <div class="input-container">
                                    <input 
                                        type="email" 
                                        placeholder="电子邮件地址" 
                                        class="w-full px-4 py-4 rounded-full text-center text-gray-600 border-2 border-gray-300 focus:outline-none focus:border-blue-500 transition-all duration-200"
                                    >
                                </div>
                                <button type="button" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
                                    继续
                                </button>
                            </form>
                            <p class="mt-8 text-gray-600">
                                还没有帐户？ 
                                <button class="text-blue-600 hover:underline">请注册</button>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 实际页面链接 -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-semibold mb-4 text-center">实际页面</h2>
                <div class="space-y-4">
                    <a href="http://127.0.0.1:14648/login" 
                       target="_blank"
                       class="block bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors text-center">
                        打开登录页面
                    </a>
                    <a href="http://127.0.0.1:14648" 
                       target="_blank"
                       class="block bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors text-center">
                        打开主页（对比按钮尺寸）
                    </a>
                </div>
                
                <div class="mt-6 bg-gray-50 p-4 rounded-lg">
                    <h3 class="font-semibold mb-2">检查要点：</h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 表单容器宽度：最大500px，90%宽度</li>
                        <li>• 容器内边距：3rem (48px)</li>
                        <li>• 输入框内边距：px-4 py-4 (16px)</li>
                        <li>• 按钮内边距：py-4 (16px垂直)</li>
                        <li>• 圆角：rounded-full</li>
                        <li>• 容器圆角：1.5rem (24px)</li>
                        <li>• 阴影：适中的投影效果</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-yellow-800 mb-2">🔍 测试说明</h3>
            <p class="text-yellow-700 text-sm mb-3">
                请对比左侧参考尺寸和右侧实际页面，确保：
            </p>
            <ol class="text-yellow-700 text-sm space-y-1">
                <li>1. 表单容器的整体大小和比例一致</li>
                <li>2. 输入框和按钮的高度、内边距相同</li>
                <li>3. 文字大小和间距保持一致</li>
                <li>4. 圆角和阴影效果相同</li>
                <li>5. 动画切换时不影响容器尺寸</li>
            </ol>
        </div>
    </div>
    
    <style>
        .form-container-reference {
            max-width: 500px;
            width: 90%;
            margin: 0 auto;
            padding: 3rem;
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
        }
    </style>
</body>
</html>

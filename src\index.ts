import { getHomePage } from './pages/home';
import { getIndexPage } from './pages/index';
import { getLoginPage } from './pages/login';

export default {
	async fetch(request, env, ctx): Promise<Response> {
		const url = new URL(request.url);
		const path = url.pathname;

		switch (path) {
			case '/':
				return new Response(getIndexPage(), {
					headers: { 'Content-Type': 'text/html' }
				});
			case '/home':
				return new Response(getHomePage(), {
					headers: { 'Content-Type': 'text/html' }
				});
			case '/login':
				return new Response(getLoginPage(), {
					headers: { 'Content-Type': 'text/html' }
				});
			case '/styles.css':
				// 读取本地CSS文件内容
				const cssContent = `
/* 输入框基础样式 - 预设2px边框防止拖动 */
.input-container input {
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.input-container input:focus {
  border-color: #3b82f6;
  outline: none;
}

.input-active {
  border-color: #3b82f6 !important;
  background-color: white;
}

.input-container {
  position: relative;
}

.input-label {
  position: absolute;
  left: 16px;
  top: -8px;
  background: white;
  padding: 0 4px;
  font-size: 14px;
  color: #3b82f6;
  transition: all 0.2s ease;
  opacity: 0;
  transform: translateY(8px);
  pointer-events: none;
  z-index: 1;
}

.input-label.active {
  opacity: 1;
  transform: translateY(0);
}

.form-container {
  max-width: 500px;
  width: 90%;
  margin: 0 auto;
  padding: 3rem;
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Additional animations for better UX */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* 防止输入框聚焦时布局拖动 */
.input-container input {
  box-sizing: border-box;
}

/* 确保所有输入框都有统一的边框 */
input[type="email"], input[type="password"] {
  border: 2px solid #d1d5db !important;
}

input[type="email"]:focus, input[type="password"]:focus {
  border-color: #3b82f6 !important;
}
				`;
				return new Response(cssContent, {
					headers: { 'Content-Type': 'text/css' }
				});
			default:
				return new Response('404 Not Found', { status: 404 });
		}
	},
} satisfies ExportedHandler<Env>;

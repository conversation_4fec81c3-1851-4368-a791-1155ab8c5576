<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Navigation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">登录页面导航测试</h1>
        
        <div class="space-y-4 mb-8">
            <a href="http://127.0.0.1:14648/login" 
               class="block bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                访问登录页面（默认）
            </a>
            
            <a href="http://127.0.0.1:14648/login#login" 
               class="block bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                访问登录页面（#login）
            </a>
            
            <a href="http://127.0.0.1:14648/login#register" 
               class="block bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors">
                访问注册页面（#register）
            </a>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto">
            <h2 class="text-lg font-semibold mb-4">测试步骤：</h2>
            <ol class="text-left text-sm text-gray-600 space-y-2">
                <li>1. 点击上面的链接测试不同的URL</li>
                <li>2. 确认每个链接只显示对应的表单</li>
                <li>3. 在登录页面点击"请注册"按钮</li>
                <li>4. 在注册页面点击"请登录"按钮</li>
                <li>5. 使用浏览器的前进/后退按钮测试</li>
            </ol>
        </div>
        
        <div class="mt-6">
            <p class="text-sm text-gray-500">
                修复内容：确保页面切换时不会同时显示多个表单
            </p>
        </div>
    </div>
</body>
</html>

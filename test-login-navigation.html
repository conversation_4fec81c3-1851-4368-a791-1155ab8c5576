<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Navigation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">登录页面导航测试</h1>
        
        <div class="space-y-4 mb-8">
            <a href="http://127.0.0.1:14648/login" 
               class="block bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                访问登录页面（默认）
            </a>
            
            <a href="http://127.0.0.1:14648/login#login" 
               class="block bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                访问登录页面（#login）
            </a>
            
            <a href="http://127.0.0.1:14648/login#register" 
               class="block bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors">
                访问注册页面（#register）
            </a>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto">
            <h2 class="text-lg font-semibold mb-4">动画优化测试步骤：</h2>
            <ol class="text-left text-sm text-gray-600 space-y-2">
                <li>1. 点击上面的链接测试不同的URL</li>
                <li>2. 确认每个链接只显示对应的表单</li>
                <li>3. 在登录页面点击"请注册"按钮，观察动画是否平滑</li>
                <li>4. 在注册页面点击"请登录"按钮，观察动画是否平滑</li>
                <li>5. 快速多次点击切换按钮，检查是否有闪烁</li>
                <li>6. 使用浏览器的前进/后退按钮测试</li>
                <li>7. 测试表单内部的切换（邮箱→密码页面）</li>
            </ol>
        </div>

        <div class="mt-6 space-y-2">
            <div class="bg-green-100 p-3 rounded-lg">
                <p class="text-sm text-green-800 font-medium">✅ 已修复的问题：</p>
                <ul class="text-xs text-green-700 mt-1 space-y-1">
                    <li>• 消除了表单切换时的闪烁效果</li>
                    <li>• 优化了动画时序，避免抖动</li>
                    <li>• 实现了平滑的退出+进入动画序列</li>
                    <li>• 增加了动画持续时间，使过渡更自然</li>
                </ul>
            </div>
            <div class="bg-blue-100 p-3 rounded-lg">
                <p class="text-sm text-blue-800 font-medium">🔧 技术改进：</p>
                <ul class="text-xs text-blue-700 mt-1 space-y-1">
                    <li>• 使用opacity而不是hidden类控制显示</li>
                    <li>• 实现了Promise-based的动画序列</li>
                    <li>• 添加了CSS will-change优化性能</li>
                    <li>• 使用绝对定位避免布局跳动</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
